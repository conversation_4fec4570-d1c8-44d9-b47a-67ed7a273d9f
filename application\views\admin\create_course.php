<script>
   const BASE_URL = "<?= base_url(); ?>";
</script>

<script>
   // Debug: Log the URL being used
   const ffmpegCheckUrl = BASE_URL + 'admin/check_ffmpeg_installed';
   console.log('Checking FFmpeg at URL:', ffmpegCheckUrl);

   fetch(ffmpegCheckUrl)
      .then(response => {
         console.log('Response status:', response.status);
         console.log('Response headers:', response.headers);

         if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
         }

         return response.json();
      })
      .then(data => {
         if (data.status === 'installed') {
            console.log('%c✔ FFmpeg is installed on the server.', 'color: green; font-weight: bold;');
            console.log(data.output.join('\n'));
         } else {
            console.log('%c✖ FFmpeg is NOT installed or not accessible.', 'color: red; font-weight: bold;');
         }
      })
      .catch(err => {
         console.error('Failed to check FFmpeg status:', err);
         console.error('URL attempted:', ffmpegCheckUrl);
      });
</script>

<style>
.component-preview.active h4 {
   background-color: var(--btn-blue) !important;
   color: var(--light) !important;
}

.component-preview.active h4 span {
   color: var(--light) !important;
}
</style>

<div class="main-container">
   <div class="create-new-course">
      <h4>Create New Module</h4>
      <div class="diff-container">
         <span>Remaining Difficulty % &mdash;</span>
         <span id="diffContainer"></span>
      </div>
   </div>

   <div class="course-container">

      <form id="courseForm" action="<?= base_url('modulecontroller/save') ?>" method="post" enctype="multipart/form-data" onsubmit="submitForm(event)">

         <div class="course-left" data-aos="fade-right">

            <div class="form-item">
               <input type="text" id="course_name" name="course_name" required placeholder="Module Name" required>
            </div>
            <div class="form-item textarea-container">
               <textarea id="description" name="description" required placeholder="Module Description" required></textarea>
            </div>


            <div class="select-wrapper form-item">
               <select id="designation" name="businessUnit" required>
                  <option value="">Loading Business Unit...</option>
               </select>
               <ion-icon name="caret-down-outline"></ion-icon>
            </div>

            <div class="select-wrapper form-item">
               <select id="categorySelect" name="categorySelect">
                  <option value="disable">Select Department</option>
               </select>
               <ion-icon name="caret-down-outline"></ion-icon>
            </div>

            <div class="select-wrapper form-item">
               <select id="plantillaAssign" name="plantillaAssign">
                  <option value="disable">Select Position</option>
               </select>
               <ion-icon name="caret-down-outline"></ion-icon>
            </div>

            <div class="form-item">
               <h4>Module Image</h4>

               <div class="image-preview">
                  <!-- Preview content will be shown here -->
                  <div id="filePreviewImage"></div>
                  <!-- Remove button (initially hidden) -->
                  <button type="button" id="removeImageButton" style="display: none;" onclick="removeImage()">
                     <ion-icon name="trash-outline"></ion-icon>
                  </button>
               </div>

               <div class="upload-box">
                  <!-- Input field for selecting a file -->
                  <input class="module_image" type="file" id="fileInputImage" name="image_url"
                     onchange="previewModuleImage('fileInputImage', 'filePreviewImage')"
                     accept=".jpg, .jpeg, .png, .webp">
               </div>
            </div>


            <div class="form-item" style="display: none;">
               <h4>Module Certificate</h4>
               <div class="image-preview">
                  <div id="filePreviewCertificate"></div>
               </div>
               <input type="file" id="fileInputCertificate" name="certificate" onchange="previewFile('fileInputCertificate', 'filePreviewCertificate')" accept=".jpg, .jpeg, .png, .webp">
            </div>

         </div>

         <div class="course-right" data-aos="fade-left">
            <h3 class="create-section-h3">Create sections</h3>
            <p>Structure your module: Each section ensures employees complete all lessons, questions, or videos before moving to the next, providing a thorough and progressive assessment.</p>

            <div id="difficultyContainer">
               <div id="totalDifficultyDisplay">
                  <span id="totalDifficultyPercentage">100</span>
               </div>
            </div>

            <div id="sections">
               <!-- Sections will be dynamically added here -->
            </div>

            <div class="add-save-container">
               <button type="button" id="addSection" class="add-section">Add Section</button>
               <button type="button" id="unhideButton" class="preview-module">Preview Module</button>
            </div>

         </div>

         <input type="hidden" id="is_published" name="is_published" value="0">


         <!-- Modal Structure -->
         <div id="publishModal" style="display: none;" class="publish-modal">
            <div class="publish-content">

               <div class="publish-item">
                  <div class="preview-container">
                     <div class="left-section">
                        <h3>Module Content</h3>
                        <div id="modal_preview_content"></div>
                     </div>
                     <div class="right-section">
                        <div id="detail_view">
                           <div id="detail_content"></div>
                        </div>
                     </div>
                  </div>
               </div>


               <div class="publish-item module-info-container">
                  <div class="module-info-item">
                     <img id="modal_image_preview" src="" alt="Image Preview" style="max-width: 100%; display: none;">
                  </div>

                  <div class="module-info-item">
                     <h3 id="modal_course_name"></h3>
                     <p id="modal_designation"></p>
                     <p id="modal_category"></p>
                     <p id="modal_plantilla"></p>
                     <p id="modal_course_description"></p>
                  </div>
               </div>


               <div class="publish-item publish-buttons">
                  <div class="publish-buttons-item">
                     <button type="button" id="closeModal">
                        <span>Close Preview</span>
                     </button>
                  </div>

                  <div class="publish-buttons-item">
                     <button type="submit" class="save-course" id="submitButton" onclick="document.getElementById('is_published').value = 1;">Publish Module</button>
                     <button type="submit" class="save-course" id="draftButton" onclick="document.getElementById('is_published').value = 0;">Save as Draft</button>
                  </div>
               </div>

            </div>
         </div>




         <script>
            // Set the base URL for redirection using PHP
            const redirectUrl = '<?= base_url('admin_course') ?>';

            function submitForm(event) {
               event.preventDefault(); // Prevent the default form submission

               // Create a FormData object from the form
               const formData = new FormData(document.getElementById('courseForm'));

               // Check for files and add to upload progress manager
               const fileId = `course_form_${Date.now()}`;
               let hasFiles = false;
               let totalSize = 0;
               let fileNames = [];

               for (let pair of formData.entries()) {
                  if (pair[1] instanceof File && pair[1].size > 0) {
                     hasFiles = true;
                     totalSize += pair[1].size;
                     fileNames.push(pair[1].name);
                  }
               }

               if (hasFiles && window.UploadProgressManager) {
                  const displayName = fileNames.length > 1 ? `${fileNames.length} files` : fileNames[0];
                  UploadProgressManager.addUpload(fileId, displayName, totalSize, 'application/octet-stream');
               }

               // Create an XMLHttpRequest object
               const xhr = new XMLHttpRequest();

               // Add upload progress tracking
               if (hasFiles && window.UploadProgressManager) {
                  xhr.upload.addEventListener('progress', function(e) {
                     if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        UploadProgressManager.updateProgress(fileId, percentComplete);
                     }
                  });
               }

               xhr.open('POST', document.getElementById('courseForm').action, true);
               xhr.onload = function() {
                  if (xhr.status >= 200 && xhr.status < 300) {
                     if (hasFiles && window.UploadProgressManager) {
                        UploadProgressManager.completeUpload(fileId);
                     }
                     // Redirect to the PHP base URL on successful form submission
                     window.location.href = redirectUrl;
                  } else {
                     if (hasFiles && window.UploadProgressManager) {
                        UploadProgressManager.errorUpload(fileId, `Upload failed: ${xhr.statusText}`);
                     }
                     // Handle error cases here
                     SmartAlerts.error('An error occurred while uploading: ' + xhr.statusText, 'Upload Failed');
                  }
               };

               xhr.onerror = function() {
                  if (hasFiles && window.UploadProgressManager) {
                     UploadProgressManager.errorUpload(fileId, 'Network error occurred');
                  }
                  SmartAlerts.error('A network error occurred. Please check your connection and try again.', 'Network Error');
               };

               xhr.send(formData);
            }
         </script>

         <script>
            let hierarchyData = {};

            $(document).ready(function() {
               // Initially disable both selects
               $('#categorySelect').prop('disabled', true);
               $('#plantillaAssign').prop('disabled', true);

               // Set loading text for business unit select
               $('#designation').html('<option value="">Loading Business Unit...</option>').prop('disabled', true);

               $.ajax({
                  url: BASE_URL + 'HR_Department/get_all_hierarchy',
                  type: 'GET',
                  dataType: 'json',
                  success: function(data) {
                     hierarchyData = data;
                     populateBusinessUnits(data);
                     $('#designation').prop('disabled', false);
                  },
                  error: function(xhr, status, error) {
                     console.error('Failed to load hierarchy data:', error);
                     $('#designation').html('<option value="">Failed to load Business Units</option>').prop('disabled', true);
                  }
               });

               // On Business Unit change
               $('#designation').on('change', function() {
                  const selectedUnit = $(this).val();

                  // Reset and disable dependent fields
                  $('#categorySelect').html('<option value="">Select Department</option>').prop('disabled', true);
                  $('#plantillaAssign').html('<option value="">Select Plantilla</option>').prop('disabled', true);

                  if (selectedUnit !== '') {
                     populateDepartments(selectedUnit);
                     $('#categorySelect').prop('disabled', false);
                  }
               });

               // On Department change
               $('#categorySelect').on('change', function() {
                  const selectedUnit = $('#designation').val();
                  const selectedDept = $(this).val();

                  $('#plantillaAssign').html('<option value="">Select Plantilla</option>').prop('disabled', true);

                  if (selectedDept !== '') {
                     populatePlantilla(selectedUnit, selectedDept);
                     $('#plantillaAssign').prop('disabled', false);
                  }
               });
            });

            function populateBusinessUnits(data) {
               const $designation = $('#designation');
               $designation.html('<option value="">Select Business Unit</option>');
               Object.keys(data).forEach(unit => {
                  $designation.append(`<option value="${unit}">${unit}</option>`);
               });
            }

            function populateDepartments(unit) {
               const $category = $('#categorySelect');
               $category.html('<option value="">Select Department</option>');
               if (hierarchyData[unit]) {
                  Object.keys(hierarchyData[unit]).forEach(dept => {
                     $category.append(`<option value="${dept}">${dept}</option>`);
                  });
               }
            }

            function populatePlantilla(unit, dept) {
               const $plantilla = $('#plantillaAssign');
               $plantilla.html('<option value="">Select Position</option>');
               if (hierarchyData[unit] && hierarchyData[unit][dept]) {
                  hierarchyData[unit][dept].forEach(role => {
                     $plantilla.append(`<option value="${role}">${role}</option>`);
                  });
               }
            }
         </script>


         <script>
            // Select the input field and the modal's target element
            const courseNameInput = document.getElementById('course_name');
            const moduleNameElement = document.getElementById('modal_course_name');

            const descriptionInput = document.getElementById('description');
            const modalDescriptionElement = document.getElementById('modal_course_description');

            const categorySelect = document.getElementById('categorySelect');
            const modelCategoryElement = document.getElementById('modal_category');

            const designationSelect = document.getElementById('designation');
            const modelDesignationElement = document.getElementById('modal_designation');

            const plantillaSelect = document.getElementById('plantillaAssign');
            const modalPlantillaElement = document.getElementById('modal_plantilla');

            const moduleImageInput = document.querySelector('.module_image');
            const modalImagePreview = document.getElementById('modal_image_preview');


            // Add an event listener to the input field to update the modal text
            courseNameInput.addEventListener('input', function() {
               moduleNameElement.textContent = courseNameInput.value || 'name_of_module'; // Default if input is empty
            });
            descriptionInput.addEventListener('input', function() {
               modalDescriptionElement.innerHTML = (descriptionInput.value || 'description_of_module').replace(/\n/g, '<br>');
            });

            categorySelect.addEventListener('change', function() {
               modelCategoryElement.textContent = categorySelect.value || 'category_of_module'; // Default if input is empty    
            });

            designationSelect.addEventListener('change', function() {
               const selectedValue = designationSelect.value;
               if (selectedValue) {
                  // Format the business unit name for display in modal
                  const parts = selectedValue.split('-');
                  if (parts.length >= 2) {
                     const abbreviation = parts[0]; // Keep abbreviation as-is (already in caps)
                     const fullName = parts.slice(1).join('-'); // Join back in case there are multiple hyphens

                     // Function to capitalize words except articles
                     const articlesAndPrepositions = ['of', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by'];
                     const formattedFullName = fullName.toLowerCase().split(' ').map((word, index) => {
                        // Always capitalize first word, or if it's not an article/preposition
                        if (index === 0 || !articlesAndPrepositions.includes(word)) {
                           return word.charAt(0).toUpperCase() + word.slice(1);
                        }
                        return word;
                     }).join(' ');

                     modelDesignationElement.textContent = `${abbreviation} - ${formattedFullName}`;
                  } else {
                     modelDesignationElement.textContent = selectedValue;
                  }
               } else {
                  modelDesignationElement.textContent = 'designation_of_module'; // Default if input is empty
               }
            });

            plantillaSelect.addEventListener('change', function() {
               modalPlantillaElement.textContent = plantillaSelect.value || 'plantilla_of_module'; // Default if input is empty
            });

            // Handle image preview
            moduleImageInput.addEventListener('change', function() {
               const file = moduleImageInput.files[0];
               if (file) {
                  const reader = new FileReader();
                  reader.onload = function(e) {
                     modalImagePreview.src = e.target.result;
                     modalImagePreview.style.display = 'block';
                  }
                  reader.readAsDataURL(file);
               } else {
                  modalImagePreview.src = '';
                  modalImagePreview.style.display = 'none';
               }
            });
         </script>


         <script>
            function previewModuleImage(inputElementId, previewElementId) {
               const fileInput = document.getElementById(inputElementId); // File input element
               const previewElement = document.getElementById(previewElementId); // Preview element
               const removeButton = document.getElementById('removeImageButton'); // Remove button element

               // Ensure previewElement exists
               if (!fileInput || !previewElement || !removeButton) {
                  console.error('Required elements are missing');
                  return;
               }

               // Clear any previous content in the preview area
               previewElement.innerHTML = '';

               // Check if a file is selected
               if (fileInput.files && fileInput.files[0]) {
                  const file = fileInput.files[0];
                  const fileType = file.type;

                  // If it's an image
                  if (fileType.startsWith('image/')) {
                     const reader = new FileReader(); // Create a FileReader to read the file

                     reader.onload = function(e) {
                        // Create an image element to display the selected file
                        const img = document.createElement('img');
                        img.src = e.target.result; // Set the src to the file's data URL
                        img.style.maxWidth = '100%'; // Limit the image's width to the container's width
                        img.style.height = 'auto'; // Maintain aspect ratio
                        previewElement.appendChild(img); // Append the image to the preview element

                        // Show the "Remove Upload" button
                        removeButton.style.display = 'block';
                     };

                     // Read the file as a data URL for preview
                     reader.readAsDataURL(file);
                  } else {
                     // Handle if it's not an image (optional)
                     previewElement.innerHTML = 'Cannot preview this file type';
                     removeButton.style.display = 'none'; // Hide the remove button if the file is not an image
                  }
               } else {
                  console.log('No file selected');
               }
            }

            // Function to remove the uploaded image and reset the file input
            function removeImage() {
               const previewElement = document.getElementById('filePreviewImage'); // Preview element
               const fileInput = document.getElementById('fileInputImage'); // File input element
               const removeButton = document.getElementById('removeImageButton'); // Remove button

               // Clear the preview content
               previewElement.innerHTML = '';

               // Reset the file input value
               fileInput.value = '';

               // Hide the "Remove Upload" button
               removeButton.style.display = 'none';
            }
         </script>





         <script>
            // Select the #menu-icon div and the .publish-content div
            const menuIcon = document.getElementById('menu-icon');
            const publishContent = document.querySelector('.publish-content');

            // Track the current width state
            let isSmallWidth = false;

            // Add a click event listener to the #menu-icon div
            menuIcon.addEventListener('click', function() {
               if (isSmallWidth) {
                  publishContent.style.width = 'calc(100% - 280px - 6px)'; // Reset to original width
               } else {
                  publishContent.style.width = 'calc(100% - 64px - 6px)'; // Set to smaller width
               }

               // Toggle the state
               isSmallWidth = !isSmallWidth;
            });
         </script>


      </form>


      <script>
         window.onload = function() {
            document.getElementById('submitButton').addEventListener('click', function() {
               document.getElementById('is_published').value = 1;
            });

            document.getElementById('draftButton').addEventListener('click', function() {
               document.getElementById('is_published').value = 0;
            });
         };
      </script>




      <script>
         // Get modal and buttons
         const publishModal = document.getElementById('publishModal');
         const unhideButton = document.getElementById('unhideButton');
         const closeModal = document.getElementById('closeModal');

         // Show the modal when unhideButton is clicked
         unhideButton.addEventListener('click', function() {
            // Check if button is visually disabled
            if ($(this).hasClass('disabled')) {
               // Validate and show warning
               const validationResult = validateMultipleChoiceQuestions();
               if (!validationResult.isValid) {
                  showValidationWarning(validationResult.errors);
               }
               return;
            }

            publishModal.style.display = 'flex';
            showSectionNamesInPreview(); // Update the section preview when modal is shown
         });

         // Hide the modal when closeModal is clicked
         closeModal.addEventListener('click', function() {
            publishModal.style.display = 'none';
         });

         // Optional: Close modal when clicking outside the modal content
         window.addEventListener('click', function(event) {
            if (event.target === publishModal) {
               publishModal.style.display = 'none';
            }
         });

         // Function to validate multiple choice questions
         function validateMultipleChoiceQuestions() {
            const errors = [];
            let isValid = true;

            // Check both short assessment (single multiple choice) and multiple choice sets
            $('.section').each(function(sectionIndex) {
               const sectionNumber = $(this).data('section-id');
               const sectionName = $(this).find('input[id^="section_name_"]').val() || `Section ${sectionNumber}`;

               // Validate short assessment multiple choice questions
               $(this).find('.multiple_choice.component-item').each(function(questionIndex) {
                  const questionNumber = questionIndex + 1;
                  const questionText = $(this).find('input[name*="[short_assessment_question]"]').val() || `Question ${questionNumber}`;
                  const hasSelectedRadio = $(this).find('input[type="radio"]:checked').length > 0;

                  if (!hasSelectedRadio) {
                     errors.push({
                        section: sectionNumber,
                        sectionName: sectionName,
                        question: questionNumber,
                        questionText: questionText,
                        type: 'short_assessment'
                     });
                     isValid = false;
                  }
               });

               // Validate multiple choice set questions
               $(this).find('.multiple_choice_set').each(function(setIndex) {
                  const setTitle = $(this).find('input[name*="[set_title]"]').val() || `Multiple Choice Set ${setIndex + 1}`;

                  $(this).find('.multiple_choice').each(function(questionIndex) {
                     const questionNumber = questionIndex + 1;
                     const questionText = $(this).find('input[name*="[question_text]"]').val() || `Question ${questionNumber}`;
                     const hasSelectedRadio = $(this).find('input[type="radio"]:checked').length > 0;

                     if (!hasSelectedRadio) {
                        errors.push({
                           section: sectionNumber,
                           sectionName: sectionName,
                           question: questionNumber,
                           questionText: questionText,
                           setTitle: setTitle,
                           type: 'multiple_choice_set'
                        });
                        isValid = false;
                     }
                  });
               });
            });

            return { isValid, errors };
         }

         // Function to show validation warning panel
         function showValidationWarning(errors) {
            let errorMessages = '';

            errors.forEach(error => {
               if (error.type === 'short_assessment') {
                  errorMessages += `<p>• ${error.sectionName} - ${error.questionText}</p>`;
               } else {
                  errorMessages += `<p>• ${error.sectionName} - ${error.setTitle} - ${error.questionText}</p>`;
               }
            });

            const warningPanel = `
               <div id="validationWarningPanel" class="validation-warning-panel">
                  <div class="warning-content">
                     <div class="warning-header">
                        <ion-icon name="warning-outline"></ion-icon>
                        <h3>Incomplete Multiple Choice Questions</h3>
                     </div>
                     <div class="warning-body">
                        <p>You have not yet selected any radio button for the following questions:</p>
                        <div class="error-list">
                           ${errorMessages}
                        </div>
                        <p>Please select the correct answer for each question before previewing the module.</p>
                     </div>
                     <div class="warning-footer">
                        <button type="button" id="closeWarningPanel" class="close-warning-btn">OK</button>
                     </div>
                  </div>
               </div>
            `;

            // Remove existing warning panel if any
            $('#validationWarningPanel').remove();

            // Add warning panel to body
            $('body').append(warningPanel);

            // Add event listener to close button
            $('#closeWarningPanel').on('click', function() {
               $('#validationWarningPanel').remove();
            });

            // Close panel when clicking outside
            $('#validationWarningPanel').on('click', function(e) {
               if (e.target === this) {
                  $(this).remove();
               }
            });
         }

         // Function to update preview button state
         function updatePreviewButtonState() {
            const validationResult = validateMultipleChoiceQuestions();
            const previewButton = $('#unhideButton');

            if (validationResult.isValid) {
               previewButton.removeClass('disabled');
               previewButton.attr('title', 'Preview Module');
            } else {
               previewButton.addClass('disabled');
               previewButton.attr('title', 'Please select correct answers for all multiple choice questions');
            }
         }

         // Initial check on page load
         $(document).ready(function() {
            updatePreviewButtonState();

            // Monitor changes to radio buttons
            $(document).on('change', 'input[type="radio"]', function() {
               updatePreviewButtonState();
            });

            // Monitor when components are added or removed
            $(document).on('DOMNodeInserted DOMNodeRemoved', function() {
               setTimeout(updatePreviewButtonState, 100);
            });
         });

         // Function to show section names and their components in the modal preview
         function showSectionNamesInPreview() {
            // Clear previous content in the modal preview
            $('#modal_preview_content').empty();
            $('#detail_content').empty(); // Clear detail view

            let firstComponentId = null; // Track the first component

            // Iterate over all section_name inputs and append their values to the modal preview
            $('[id^="section_name_"]').each(function() {
               let sectionName = $(this).val() || 'Default Section Name';
               let sectionId = $(this).attr('id').split('_')[2]; // Extract the section ID

               // Create the section preview with the section name
               let previewHtml = `<div id="section-preview-${sectionId}" class="section-preview" data-preview-id="${sectionId}">
                                <h3>${sectionName}</h3>
                                <div class="component-previews"></div>
                            </div>`;

               // Append section preview to modal content
               $('#modal_preview_content').append(previewHtml);

               // Now, find the components inside this section and preview them
               $(`#section_name_${sectionId}`).closest('.section').find('.component-item').each(function(index) {
                  let componentTitle = $(this).find('input[type="text"]').val() || 'Untitled Component';
                  let componentType = $(this).find('h4').first().text() || 'Untitled Component';
                  let componentId = `${sectionId}_${index}`; // Unique ID for each component

                  // Store the first component ID
                  if (firstComponentId === null) {
                     firstComponentId = componentId;
                  }

                  // Create a preview for each component with a clickable title
                  let componentPreviewHtml = `<div class="component-preview" data-component-id="${componentId}">
                                            <h4>${componentType}: <span>${componentTitle}</span></h4>
                                            </div>`;

                  // Append the component preview under the corresponding section
                  $(`#section-preview-${sectionId} .component-previews`).append(componentPreviewHtml);

                  // Store component details in a data attribute for later retrieval
                  $(this).attr('data-component-id', componentId);
               });
            });

            // Add click event listeners to components
            $('.component-preview').click(function() {
               // Remove active class from all component previews
               $('.component-preview').removeClass('active');
               // Add active class to clicked component
               $(this).addClass('active');

               let componentId = $(this).data('component-id');
               displayComponentDetails(componentId);
            });

            // Show the first component details initially if there are components
            if (firstComponentId !== null) {
               // Add active class to the first component
               $(`.component-preview[data-component-id="${firstComponentId}"]`).addClass('active');
               displayComponentDetails(firstComponentId);
            }
         }

         // Function to display details of the clicked component on the right side
         function displayComponentDetails(componentId) {
            // Find the component by its unique ID
            let component = $(`[data-component-id="${componentId}"]`).closest('.component-item');

            // Generate and display the preview content for the selected component
            let detailsHtml = generateComponentPreview(component);
            $('#detail_content').html(detailsHtml);
         }

         function formatInputText(text) {
            return escapeHtml(text).replace(/\n/g, '<br>');
         }

         // Function to generate preview content for each component
         function generateComponentPreview(component) {

            let previewContent = '';

            let componentType = component.find('h4').first().text().split(':')[0] || 'untitled';
            let formattedComponentType = componentType.toLowerCase().replace(/\s+/g, '-');

            // Handle multiple-choice components
            if (formattedComponentType === 'multiple-choice') {
               // Initialize questions

               previewContent += `<p class="multiple-set-title">Multiple Choice Set: ${component.find('input[name*="[set_title]"]').val() || 'No Multiple Choice Set'}</p>`;
               let questions = component.find('input[name*="[question_text]"]');

               // Iterate through each question
               questions.each(function(questionIndex) {
                  let questionText = $(this).val();
                  if (questionText) {
                     previewContent += `<div class="text-content question-title">Question: ${questionText}</div>`;
                  }

                  // Find choices for the current question
                  let questionDiv = $(this).closest('.multiple_choice');
                  let choices = questionDiv.find('.choice');

                  // Iterate through each choice
                  choices.each(function(choiceIndex) {
                     let choiceText = $(this).find('input[type="text"]').val();
                     let isChecked = $(this).find('input[type="radio"]').is(':checked'); // Check if the radio button is selected

                     if (choiceText) {
                        // Render the choice as a radio button
                        previewContent += `
                            <div class="text-content user-answer">
                                <input type="radio" name="question-${questionIndex}" value="${choiceText}" ${isChecked ? '' : 'disabled'} ${isChecked ? 'checked' : ''}>
                                <label>${choiceText}</label>
                            </div>
                        `;
                     }
                  });
               });

               previewContent += `<p class="note">Note: Only 1 question will be shown to the employee. It will be randomly shown.</p>`;
               previewContent += `<button class="submit-answer-mc" disabled type="button">Submit Answer</button>`;
            } else {
               // Handle other component types (existing functionality)
               component.find('input, textarea').each(function() {
                  let inputType = $(this).attr('type');
                  let inputText = $(this).val();

                  if (inputType === 'text' || inputType === 'number' || $(this).is('textarea')) {
                     // If it's a text input or textarea, check if it's a URL
                     if (isValidURL(inputText)) {
                        previewContent += `<div class="text-content ${formattedComponentType}">${inputText}</div>`;
                     } else {
                        previewContent += `<div class="text-content ${formattedComponentType}">${formatInputText(inputText)}</div>`;
                     }
                  } else if (inputType === 'file') {
                     // Handle file input preview (image, video, etc.)
                     let fileInput = $(this)[0];

                     // Check if a file has been selected (for preview)
                     if (fileInput.files && fileInput.files[0]) {
                        let file = fileInput.files[0];
                        let fileUrl = URL.createObjectURL(file);

                        // Display different previews based on file type
                        if (file.type.startsWith('image/')) {
                           previewContent += `<img src="${fileUrl}" alt="Image Preview" style="max-width: 100%;"><br>`;
                        } else if (file.type.startsWith('video/')) {
                           previewContent += `<video src="${fileUrl}" controls style="max-width: 100%;"></video><br>`;
                        } else {
                           previewContent += `<p>File: ${file.name}</p><br>`;
                        }
                     }
                  } else if (inputType === 'url') {
                     if (inputText && isValidURL(inputText)) {
                        // Check if inputText is not empty and is a valid URL
                        let modifiedUrl = modifyVideoURL(inputText);
                        previewContent += `<iframe src="${modifiedUrl}" frameborder="0" allowfullscreen></iframe><br>`;
                     }
                  }
               });
            }

            if (!previewContent) {
               previewContent = '<p>No content to preview.</p>';
            }

            return previewContent;
         }





         // Utility function to validate if a string is a valid URL
         function isValidURL(string) {
            var res = string.match(/^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i);
            return (res !== null);
         }

         // URL Validation Function with Floating Tips
         function validateURL(input) {
            const url = input.value.trim();
            const container = input.parentElement;

            // Remove existing validation tip
            const existingTip = container.querySelector('.url-validation-tip');
            if (existingTip) {
                existingTip.remove();
            }

            // Remove existing validation classes
            input.classList.remove('valid-url', 'invalid-url');

            if (url === '') {
                // Empty input - no validation needed
                return;
            }

            // Create validation tip element
            const tip = document.createElement('div');
            tip.className = 'url-validation-tip';

            if (isValidURL(url)) {
                // Valid URL
                tip.classList.add('valid');
                tip.textContent = 'Valid URL';
                input.classList.add('valid-url');
            } else {
                // Invalid URL
                tip.classList.add('invalid');
                tip.textContent = 'Invalid URL';
                input.classList.add('invalid-url');
            }

            // Add tip to container
            container.appendChild(tip);

            // Show tip with animation
            setTimeout(() => {
                tip.classList.add('show');
            }, 10);

            // Hide tip after 3 seconds
            setTimeout(() => {
                tip.classList.remove('show');
                setTimeout(() => {
                    if (tip.parentElement) {
                        tip.remove();
                    }
                }, 300);
            }, 3000);
         }

         // Function to wrap URL inputs and add validation
         function setupURLValidation() {
            // Find all URL inputs
            const urlInputs = document.querySelectorAll('input[type="url"]');

            urlInputs.forEach(input => {
                // Wrap input in container if not already wrapped
                if (!input.parentElement.classList.contains('url-input-container')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'url-input-container';
                    input.parentNode.insertBefore(wrapper, input);
                    wrapper.appendChild(input);
                }

                // Add event listeners
                input.addEventListener('input', function() {
                    validateURL(this);
                });

                input.addEventListener('blur', function() {
                    validateURL(this);
                });
            });
         }

         // Utility function to detect if a URL is a YouTube URL
         function isYouTubeURL(url) {
            const youtubePattern1 = /^(https?:\/\/)?(www\.)?youtube\.com\/watch\?v=[\w-]+/; // youtube.com/watch?v= format
            const youtubePattern2 = /^(https?:\/\/)?(www\.)?youtu\.be\/[\w-]+/; // youtu.be/ format
            const youtubePattern3 = /^(https?:\/\/)?(www\.)?youtube\.com\/embed\/[\w-]+/; // youtube.com/embed/ format
            return url.match(youtubePattern1) !== null || url.match(youtubePattern2) !== null || url.match(youtubePattern3) !== null;
         }

         // Utility function to detect if a URL is a Vimeo URL
         function isVimeoURL(url) {
            const vimeoPattern = /^(https?:\/\/)?(www\.)?vimeo\.com\/(\d+)/;
            return url.match(vimeoPattern) !== null;
         }

         // Utility function to detect if a URL is a Dailymotion URL (supports both formats)
         function isDailymotionURL(url) {
            const dailymotionPattern1 = /^(https?:\/\/)?(www\.)?dai.ly\/[a-z0-9]+/; // dai.ly format
            const dailymotionPattern2 = /^(https?:\/\/)?(www\.)?dailymotion.com\/video\/[a-z0-9]+/; // dailymotion.com format
            return url.match(dailymotionPattern1) !== null || url.match(dailymotionPattern2) !== null;
         }

         // Function to modify the YouTube URL before embedding
         function modifyVideoURL(url) {
            if (isYouTubeURL(url)) {
               // Extract YouTube video ID and create proper embed URL
               let videoId = '';

               // Handle youtube.com/watch?v= format
               if (url.includes('youtube.com/watch?v=')) {
                  const urlParams = new URLSearchParams(url.split('?')[1]);
                  videoId = urlParams.get('v');
               }
               // Handle youtu.be/ format
               else if (url.includes('youtu.be/')) {
                  videoId = url.split('youtu.be/')[1].split('?')[0].split('&')[0];
               }
               // Handle youtube.com/embed/ format (already correct)
               else if (url.includes('youtube.com/embed/')) {
                  return url; // Already in embed format
               }

               // Return proper embed URL
               if (videoId) {
                  return `https://www.youtube.com/embed/${videoId}`;
               }
               return url; // Fallback to original URL if extraction fails
            } else if (isVimeoURL(url)) {
               // Modify Vimeo URL from "vimeo.com/{videoId}" to "player.vimeo.com/video/{videoId}"
               let videoId = url.split('/').pop();
               return `https://player.vimeo.com/video/${videoId}`;
            } else if (isDailymotionURL(url)) {
               // Modify Dailymotion URL from both "dai.ly/{videoId}" and "dailymotion.com/video/{videoId}" to "https://geo.dailymotion.com/player.html?video={videoId}"
               let videoId = url.split('/').pop(); // Extract video ID
               return `https://geo.dailymotion.com/player.html?video=${videoId}`;
            } else {
               // If not a video URL, return the original URL
               return url;
            }
         }

         // Function to embed a YouTube video
         function embedYouTubeVideo(url) {
            let modifiedUrl = modifyVideoURL(url);
            return `<iframe width="560" height="315" src="${modifiedUrl}" frameborder="0" allowfullscreen></iframe><br>`;
         }

         // Function to embed a Vimeo video
         function embedVimeoVideo(url) {
            let modifiedUrl = modifyVideoURL(url);
            return `<iframe width="560" height="315" src="${modifiedUrl}" frameborder="0" allowfullscreen></iframe><br>`;
         }

         function embedDailymotionVideo(url) {
            try {
               let modifiedUrl = modifyVideoURL(url); // Modify the URL if needed
               return `<iframe width="560" height="315" src="${modifiedUrl}" frameborder="0" allowfullscreen sandbox="allow-scripts allow-same-origin allow-popups allow-forms"></iframe><br>`;
            } catch (e) {
               console.log("Caught error while embedding Dailymotion video: ", e); // Handle the error gracefully
               return "<p>Sorry, there was an issue loading the video.</p>"; // Show a fallback message
            }
         }
      </script>




      <script>
         const submitButton = document.getElementById('submitButton');
         const parentElement = document.getElementById('totalDifficultyPercentage').parentNode;
         let tooltip = null;

         function checkDifficulty() {
            const totalDifficultyPercentage = parentElement.querySelector('#totalDifficultyPercentage');
            if (totalDifficultyPercentage.textContent === '0') {
               submitButton.disabled = false;
               submitButton.style.cursor = 'pointer';
               submitButton.style.opacity = '1';
               submitButton.title = '';
               if (tooltip) {
                  submitButton.removeChild(tooltip);
                  tooltip = null;
               }
            } else {
               submitButton.disabled = true;
               submitButton.style.cursor = 'not-allowed';

               if (!tooltip) {
                  tooltip = document.createElement('div');
                  tooltip.classList.add('tooltip');
                  tooltip.textContent = 'Add Section Difficulty';
                  submitButton.appendChild(tooltip);
               }
            }
         }

         // Call the function initially
         checkDifficulty();

         // Create a MutationObserver to observe changes to the parent element
         const observer = new MutationObserver(checkDifficulty);
         observer.observe(parentElement, {
            childList: true,
            subtree: true
         });
      </script>

      <script>
         var target = document.getElementById('totalDifficultyPercentage');
         var container = document.getElementById('diffContainer');

         // Check if observer already exists
         if (!window.diffObserver) {
            container.innerText = target.innerText;

            // Create and assign to global scope so it's not re-declared
            window.diffObserver = new MutationObserver(function(mutations) {
               mutations.forEach(function(mutation) {
                  if (mutation.type === 'childList' || mutation.type === 'characterData') {
                     container.innerText = target.innerText;
                  }
               });
            });

            window.diffObserver.observe(target, {
               childList: true,
               characterData: true,
               subtree: true
            });
         }
      </script>

      <!-- <script>
         // Function to update #diffContainer based on #totalDifficultyPercentage
         function updateDiffContainer() {
            // Get the value of #totalDifficultyPercentage
            var totalDifficulty = document.getElementById('totalDifficultyPercentage').innerText;

            // Update the content of #diffContainer
            document.getElementById('diffContainer').innerText = totalDifficulty;
         }

         // Call the function to update initially
         updateDiffContainer();
         setInterval(updateDiffContainer, 1000); // Update every second
      </script> -->


      <script>
         document.getElementById('courseForm').addEventListener('submit', function() {
            // Optionally disable the submit button to prevent multiple submissions
            document.getElementById('submitButton').disabled = true;
         });
      </script>

      <script>
         $(document).ready(function() {
            let sectionCounter = 1;
            let sequenceCounter = 1;
            const initialTotalDifficulty = 100;
            $('#totalDifficultyPercentage').text(initialTotalDifficulty);

            // Add initial section
            addSection();

            // Setup URL validation for any existing URL inputs
            setupURLValidation();

            // Add section functionality
            $('#addSection').click(function() {
               addSection();
            });

            function addSection() {
               let sectionHtml = `
            <div class="section" data-section-id="${sectionCounter}">
                <div class="section-top-controls">
                    <button type="button" class="toggle-button">
                        <img src="<?= base_url('assets/icons/Collapse.webp'); ?>" alt="Toggle" width="12">
                        <span class="toggle-text">Collapse</span>
                    </button>
                    <button type="button" class="removeSection">Remove Section</button>
                    <button type="button" class="confirmRemove" style="display:none;">Confirm</button>
                    <button type="button" class="cancelRemove" style="display:none;">Cancel</button>
                </div>
                <div class="section-count-container">
                    <h4>Section<span class="section-hashtag">#</span><span class="sectionCounter">${sectionCounter}</span></h4>
                    <div class="section-difficulty-controls">
                        <span class="difficulty-label">Section Difficulty</span>
                        <input type="number" class="section-difficulty-input show-spinner" name="sections[${sectionCounter}][difficulty]" min="0" max="100" value="0">
                        <input type="range" class="section-slider" min="0" max="100" value="0">
                    </div>
                </div>

                <div class="section-content">
                    <input type="text" id="section_name_${sectionCounter}" name="sections[${sectionCounter}][section_name]" placeholder="Section Name" class="section-input" required>
                </div>
  
                <div class="section-components"></div>

                <div class="dynamic-button">
                    <div class="btn-container">
                        <button type="button" class="showLearningContent"><ion-icon name="copy-outline"></ion-icon><span>Learning Content</span></button>
                        <button type="button" class="showAssessment"><ion-icon name="newspaper-outline"></ion-icon><span>Short Assessment</span></button>
                    </div>
                </div>

                <div class="dynamic-panel">
                    <div class="learning-content" style="display: none;">
                        <button type="button" class="addShortReading">Short Reading</button>
                        <button type="button" class="addShortVideo">Short Video</button>
                        <button type="button" class="addLectureCast">Lecture Cast</button>
                        <button type="button" class="addPodcast">Podcast</button>
                    </div>

                    <!-- Assessment buttons -->
                    <div class="assessment" style="display: none;">
                        <button type="button" class="addReflectiveWriting">Narrative Writing</button>
                        <button type="button" class="addMultipleChoiceSet">Add Multiple Choice Set</button>
                        <button type="button" class="addShortAssessment" style="display: none;">Multiple Choice</button>
                    </div>
                </div>

            </div>
            `;
               $('#sections').append(sectionHtml);
               sectionCounter = $('.section').length + 1;
               updateTotalDifficulty();

            }





            // Remove section functionality
            $('#sections').on('click', '.removeSection', function() {
               // Hide the current remove button and show confirm/cancel buttons
               $(this).hide(); // Hide the Remove Section button
               $(this).siblings('.confirmRemove, .cancelRemove').show(); // Show Confirm and Cancel buttons
            });

            // Confirm removal functionality
            $('#sections').on('click', '.confirmRemove', function() {
               // Remove the section
               $(this).closest('.section').remove();
               reindexSections(); // Re-index sections after removal
               updateTotalDifficulty();
            });

            // Cancel removal functionality
            $('#sections').on('click', '.cancelRemove', function() {
               // Hide Confirm and Cancel buttons and show the Remove Section button again
               $(this).hide(); // Hide Cancel button
               $(this).siblings('.confirmRemove').hide(); // Hide Confirm button
               $(this).siblings('.removeSection').show(); // Show the Remove Section button
            });






            // Update the total difficulty percentage based on section sliders and inputs
            function updateTotalDifficulty() {
               let totalUsedDifficulty = 0;

               // Calculate total used difficulty
               $('.section-difficulty-input').each(function() {
                  totalUsedDifficulty += parseInt($(this).val());
               });

               // Calculate remaining difficulty
               let remainingDifficulty = initialTotalDifficulty - totalUsedDifficulty;

               // Ensure remaining difficulty is not negative
               if (remainingDifficulty < 0) {
                  remainingDifficulty = 0;
               }

               // Update the total difficulty display
               $('#totalDifficultyPercentage').text(remainingDifficulty);
            }

            // Update difficulty percentage display on slider change
            $('#sections').on('input', '.section-slider', function() {
               let currentValue = parseInt($(this).val());
               $(this).siblings('.section-difficulty-input').val(currentValue); // Update input field

               // Update total difficulty after slider change
               updateTotalDifficulty();

               // Restrict the slider value if it exceeds the remaining allowed difficulty
               restrictSlider($(this));
            });

            // Update difficulty percentage display on manual input change
            $('#sections').on('input', '.section-difficulty-input', function() {
               let currentValue = parseInt($(this).val());
               $(this).siblings('.section-slider').val(currentValue); // Update slider

               // Ensure the input value does not exceed 100
               if (currentValue > 100) {
                  $(this).val(100);
                  currentValue = 100;
               }

               // Update total difficulty after input change
               updateTotalDifficulty();

               // Restrict the input value if it exceeds the remaining allowed difficulty
               restrictInput($(this));
            });

            // Restrict slider value if it exceeds the remaining allowed difficulty
            function restrictSlider(slider) {
               let totalUsedDifficulty = 0;
               $('.section-slider').each(function() {
                  totalUsedDifficulty += parseInt($(this).val());
               });
               let remainingDifficulty = initialTotalDifficulty - totalUsedDifficulty + parseInt(slider.val());
               if (parseInt(slider.val()) > remainingDifficulty) {
                  slider.val(remainingDifficulty);
                  slider.siblings('.section-difficulty-input').val(remainingDifficulty); // Sync input field
               }
            }

            // Restrict input value if it exceeds the remaining allowed difficulty
            function restrictInput(input) {
               let totalUsedDifficulty = 0;
               $('.section-difficulty-input').each(function() {
                  totalUsedDifficulty += parseInt($(this).val());
               });
               let remainingDifficulty = initialTotalDifficulty - totalUsedDifficulty + parseInt(input.val());
               if (parseInt(input.val()) > remainingDifficulty) {
                  input.val(remainingDifficulty);
                  input.siblings('.section-slider').val(remainingDifficulty); // Sync slider
               }
            }

            // Initial update to ensure sliders and inputs are set correctly
            updateTotalDifficulty();
            // Re-index sections to maintain correct section IDs
            function reindexSections() {
               let newSectionCounter = 1; // Start from 1
               $('.section').each(function() {
                  $(this).attr('data-section-id', newSectionCounter);
                  $(this).find('span.sectionCounter').text(newSectionCounter); // Update displayed section number
                  $(this).find('input[name^="sections"]').attr('name', `sections[${newSectionCounter}][section_name]`);
                  reindexComponents($(this)); // Reindex all components within the section
                  newSectionCounter++;
               });
               sectionCounter = newSectionCounter; // Update sectionCounter to the next available number
            }

            function reindexComponents(sectionDiv) {
               // This function will reindex only the components within the sectionDiv
               let componentTypes = ['short_reading', 'short_video', 'lecture_cast', 'podcast', 'reflective_writing', 'short_assessment', 'multiple_choice_set'];

               componentTypes.forEach(type => {
                  sectionDiv.find(`.${type}.component-item`).each(function(index) {
                     let namePrefix = `sections[${sectionDiv.data('section-id')}][${type}s][${index + 1}]`;
                     $(this).find('input, textarea').each(function() {
                        let name = $(this).attr('name');
                        let newName = name.replace(/sections\[\d+\]\[.*?\]\[\d+\]/, namePrefix);
                        $(this).attr('name', newName);
                     });
                  });
               });
            }

            function addItem(sectionDiv, itemType, itemHtml) {
               sectionDiv.find('.section-components').append(itemHtml);
               updateSequenceNumbers(sectionDiv);
            }


            $('#sections').on('click', '.showLearningContent', function() {
               let sectionDiv = $(this).closest('.section');
               let learningContent = sectionDiv.find('.learning-content');
               let assessmentContent = sectionDiv.find('.assessment');

               learningContent.toggle(); // Show if hidden, hide if shown
               assessmentContent.hide(); // Always hide Assessment
            });

            $('#sections').on('click', '.showAssessment', function() {
               let sectionDiv = $(this).closest('.section');
               let learningContent = sectionDiv.find('.learning-content');
               let assessmentContent = sectionDiv.find('.assessment');

               assessmentContent.toggle();
               learningContent.hide();
            });


            $(document).ready(function() {
               $('#sections').on('click', '.toggle-button', function(event) {
                  event.preventDefault();

                  let sectionDiv = $(this).closest('.section');
                  sectionDiv.toggleClass('collapsed');

                  // Update the button icon and text based on the section's collapsed state
                  let img = $(this).find('img');
                  let text = $(this).find('.toggle-text');
                  if (sectionDiv.hasClass('collapsed')) {
                     img.attr('src', '<?= base_url('assets/icons/Expand.webp'); ?>');
                     text.text('Expand');
                  } else {
                     img.attr('src', '<?= base_url('assets/icons/Collapse.webp'); ?>');
                     text.text('Collapse');
                  }
               });
            });




            // Add Short Reading functionality
            $('#sections').on('click', '.addShortReading', function() {
               let sectionDiv = $(this).closest('.section');
               let lessonCounter = sectionDiv.find('.lesson.component-item').length + 1;

               // Generate unique IDs for the file input and preview div
               let uniqueId = 'fileInputShortReading_' + sectionDiv.data('section-id') + '_' + lessonCounter + '_' + sequenceCounter;
               let previewId = 'filePreviewShortReading_' + sectionDiv.data('section-id') + '_' + lessonCounter + '_' + sequenceCounter;

               let shortReadingHTML = `
                <div class="lesson component-item">
                    <div class="lesson-container short-reading-color">
                        <h4>Short Reading</h4>
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][short_reading][${lessonCounter}][short_reading_title]" placeholder="Title">
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][short_reading][${lessonCounter}][short_reading_subtitle]" placeholder="Subtitle (Optional)">
                        <textarea name="sections[${sectionDiv.data('section-id')}][short_reading][${lessonCounter}][short_reading_content]" placeholder="Content"></textarea>
                        <div class="url-input-container">
                            <input type="url" name="sections[${sectionDiv.data('section-id')}][short_reading][${lessonCounter}][short_reading_url]" placeholder="Embed URL">
                        </div>
                        <input id="${uniqueId}" onchange="previewFile('${uniqueId}', '${previewId}')" type="file" name="sections[${sectionDiv.data('section-id')}][short_reading][${lessonCounter}][short_reading_file]" class="upload-box" placeholder="Upload Media (Image, Video, Document, and/or Illustration)" accept=".jpg, .jpeg, .png, .webp, .mp4, .avi, .webm, .mov, .pdf">

                        <div class="reading-preview">
                            <div id="${previewId}" class="file-content component-content"></div> 

                            <div id="loading-panel-${previewId}" class="loading-panel">
                                <div class="loading-bar">
                                    <div class="progress"></div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="sections[${sectionDiv.data('section-id')}][short_reading][${lessonCounter}][sequence_num]" value="${sequenceCounter}">
                        <div class="component-controls">
                            <button type="button" class="swapUp" title="Move Up"><ion-icon name="chevron-up-outline"></ion-icon></button>
                            <button type="button" class="swapDown" title="Move Down"><ion-icon name="chevron-down-outline"></ion-icon></button>
                            <button type="button" class="removeComponent">Remove Component</button>
                        </div>
    
                    </div>
                </div>
            `;
               addItem(sectionDiv, 'short_reading', shortReadingHTML);
               setupURLValidation(); // Setup URL validation for newly added inputs
               sequenceCounter++;
            });


            // Add Short Video functionality
            $('#sections').on('click', '.addShortVideo', function() {
               let sectionDiv = $(this).closest('.section');
               let mediaCounter = sectionDiv.find('.media.component-item').length + 1;

               // Generate unique IDs for the file input and preview div
               let uniqueId = 'fileInputShortVideo_' + sectionDiv.data('section-id') + '_' + mediaCounter + '_' + sequenceCounter;
               let previewId = 'filePreviewShortVideo_' + sectionDiv.data('section-id') + '_' + mediaCounter + '_' + sequenceCounter;

               let shortVideoHTML = `
                <div class="media component-item">
                    <div class="media-container short-video-color">
                        <h4>Short Video</h4>
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][short_video][${mediaCounter}][short_video_title]" placeholder="Title">
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][short_video][${mediaCounter}][short_video_subtitle]" placeholder="Subtitle (Optional)">
                        <textarea name="sections[${sectionDiv.data('section-id')}][short_video][${mediaCounter}][short_video_content]" placeholder="Content"></textarea>
                        <div class="url-input-container">
                            <input type="url" name="sections[${sectionDiv.data('section-id')}][short_video][${mediaCounter}][short_video_url]" placeholder="Embed URL">
                        </div>
                        <input id="${uniqueId}" onchange="previewFile('${uniqueId}', '${previewId}')" type="file" name="sections[${sectionDiv.data('section-id')}][short_video][${mediaCounter}][short_video_file]" class="upload-box" placeholder="Upload Media (Image, Video, Document, and/or Illustration)" accept=".jpg, .jpeg, .png, .webp, .mp4, .avi, .webm, .mov, .pdf">

                        <div class="reading-preview">
                            <div id="${previewId}" class="file-content component-content"></div> 

                            <div id="loading-panel-${previewId}" class="loading-panel">
                                <div class="loading-bar">
                                    <div class="progress"></div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="sections[${sectionDiv.data('section-id')}][short_video][${mediaCounter}][sequence_num]" value="${sequenceCounter}">
                        <div class="component-controls">
                            <button type="button" class="swapUp" title="Move Up"><ion-icon name="chevron-up-outline"></ion-icon></button>
                            <button type="button" class="swapDown" title="Move Down"><ion-icon name="chevron-down-outline"></ion-icon></button>
                            <button type="button" class="removeComponent">Remove Component</button>
                        </div>
                    </div>
                </div>
            `;
               addItem(sectionDiv, 'short_video', shortVideoHTML);
               setupURLValidation(); // Setup URL validation for newly added inputs
               sequenceCounter++;
            });


            // Add Lecture Cast functionality
            $('#sections').on('click', '.addLectureCast', function() {
               let sectionDiv = $(this).closest('.section');
               let urlCounter = sectionDiv.find('.url.component-item').length + 1;

               // Generate unique IDs for the file input and preview div
               let uniqueId = 'fileInputLectureCast_' + sectionDiv.data('section-id') + '_' + urlCounter + '_' + sequenceCounter;
               let previewId = 'filePreviewLectureCast_' + sectionDiv.data('section-id') + '_' + urlCounter + '_' + sequenceCounter;

               let lectureCastHTML = `
                <div class="url component-item">
                    <div class="url-container lecture-cast-color">
                        <h4>Lecture Cast</h4>
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][lecture_cast][${urlCounter}][lecture_cast_title]" placeholder="Title">
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][lecture_cast][${urlCounter}][lecture_cast_subtitle]" placeholder="Subtitle (Optional)">
                        <textarea name="sections[${sectionDiv.data('section-id')}][lecture_cast][${urlCounter}][lecture_cast_content]" placeholder="Content"></textarea>
                        <div class="url-input-container">
                            <input type="url" name="sections[${sectionDiv.data('section-id')}][lecture_cast][${urlCounter}][lecture_cast_url]" placeholder="Embed URL">
                        </div>
                        <input id="${uniqueId}" onchange="previewFile('${uniqueId}', '${previewId}')" type="file" name="sections[${sectionDiv.data('section-id')}][lecture_cast][${urlCounter}][lecture_cast_file]" class="upload-box" placeholder="Title" accept=".jpg, .jpeg, .png, .webp, .mp4, .avi, .webm, .mov, .pdf">

                        <div class="reading-preview">
                            <div id="${previewId}" class="file-content component-content"></div> 

                            <div id="loading-panel-${previewId}" class="loading-panel">
                                <div class="loading-bar">
                                    <div class="progress"></div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="sections[${sectionDiv.data('section-id')}][lecture_cast][${urlCounter}][sequence_num]" value="${sequenceCounter}">
                        <div class="component-controls">
                            <button type="button" class="swapUp" title="Move Up"><ion-icon name="chevron-up-outline"></ion-icon></button>
                            <button type="button" class="swapDown" title="Move Down"><ion-icon name="chevron-down-outline"></ion-icon></button>
                            <button type="button" class="removeComponent">Remove Component</button>
                        </div>
                    </div>
                </div>
            `;

               addItem(sectionDiv, 'lecture_cast', lectureCastHTML);
               setupURLValidation(); // Setup URL validation for newly added inputs
               sequenceCounter++;
            });



            // Add Podcast functionality
            $('#sections').on('click', '.addPodcast', function() {
               let sectionDiv = $(this).closest('.section');
               let podcastCounter = sectionDiv.find('.podcast.component-item').length + 1;

               // Generate unique IDs for the file input and preview div
               let uniqueId = 'fileInputPodcast_' + sectionDiv.data('section-id') + '_' + podcastCounter + '_' + sequenceCounter;
               let previewId = 'filePreviewPodcast_' + sectionDiv.data('section-id') + '_' + podcastCounter + '_' + sequenceCounter;

               let podcastHtml = `
                <div class="podcast component-item">
                    <div class="podcast-container podcast-color">
                        <h4>Podcast</h4>
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][podcast][${podcastCounter}][podcast_title]" placeholder="Title">
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][podcast][${podcastCounter}][podcast_subtitle]" placeholder="Subtitle (Optional)">
                        <textarea name="sections[${sectionDiv.data('section-id')}][podcast][${podcastCounter}][podcast_content]" placeholder="Content"></textarea>
                        <div class="url-input-container">
                            <input type="url" name="sections[${sectionDiv.data('section-id')}][podcast][${podcastCounter}][podcast_url]" placeholder="Embed URL">
                        </div>
                        <input id="${uniqueId}" onchange="previewFile('${uniqueId}', '${previewId}')" type="file" name="sections[${sectionDiv.data('section-id')}][podcast][${podcastCounter}][podcast_file]" class="upload-box" placeholder="Upload Podcast" accept=".jpg, .jpeg, .png, .webp, .mp4, .avi, .webm, .mov, .pdf">

                        <div class="reading-preview">
                            <div id="${previewId}" class="file-content component-content"></div> 

                            <div id="loading-panel-${previewId}" class="loading-panel">
                                <div class="loading-bar">
                                    <div class="progress"></div>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="sections[${sectionDiv.data('section-id')}][podcast][${podcastCounter}][sequence_num]" value="${sequenceCounter}">
                        <div class="component-controls">
                            <button type="button" class="swapUp" title="Move Up"><ion-icon name="chevron-up-outline"></ion-icon></button>
                            <button type="button" class="swapDown" title="Move Down"><ion-icon name="chevron-down-outline"></ion-icon></button>
                            <button type="button" class="removeComponent">Remove Component</button>
                        </div>
                    </div>
                </div>
            `;
               addItem(sectionDiv, 'podcast', podcastHtml);
               setupURLValidation(); // Setup URL validation for newly added inputs
               sequenceCounter++;
            });




            // Add Narrative Writing functionality
            $('#sections').on('click', '.addReflectiveWriting', function() {
               let sectionDiv = $(this).closest('.section');
               let essayCounter = sectionDiv.find('.essay.component-item').length + 1;
               let essayHtml = `
                <div class="essay component-item">
                    <div class="essay-container reflective-writing-color">
                        <h4>Narrative Writing</h4>
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][reflective_writing][${essayCounter}][reflective_writing_title]" placeholder="Title">
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][reflective_writing][${essayCounter}][reflective_writing_question]" placeholder="Question">

                        <input class="narrative-attachment" type="file" name="sections[${sectionDiv.data('section-id')}][reflective_writing][${essayCounter}][reflective_writing_image]" class="upload-box" placeholder="Attach an image (Optional)" accept=".jpg, .jpeg, .png, .webp">
                        
                        <div class="pt_Quantity">
                            <input type="number" name="sections[${sectionDiv.data('section-id')}][reflective_writing][${essayCounter}][reflective_writing_limit]" placeholder="Number of words" min="0" class="show-spinner">
                        </div>

                        <input type="hidden" name="sections[${sectionDiv.data('section-id')}][reflective_writing][${essayCounter}][sequence_num]" value="${sequenceCounter}">
                        <div class="component-controls">
                            <button type="button" class="swapUp" title="Move Up"><ion-icon name="chevron-up-outline"></ion-icon></button>
                            <button type="button" class="swapDown" title="Move Down"><ion-icon name="chevron-down-outline"></ion-icon></button>
                            <button type="button" class="removeComponent">Remove Component</button>
                        </div>
                    </div>
                </div>
            `;
               addItem(sectionDiv, 'reflective_writing', essayHtml);
               sequenceCounter++;
            });




            // Add Short Assessment functionality
            $('#sections').on('click', '.addShortAssessment', function() {
               let sectionDiv = $(this).closest('.section');
               let multipleChoiceCounter = sectionDiv.find('.multiple_choice.component-item').length + 1;
               let multipleChoiceHtml = `
                <div class="multiple_choice component-item">
                    <div class="choice-container">
                        <h4>Multiple Choice</h4>
                        <input type="text" name="sections[${sectionDiv.data('section-id')}][short_assessment][${multipleChoiceCounter}][short_assessment_question]" placeholder="Question">
                        
                        <div class="choices-container">
                            ${generateInitialChoices(multipleChoiceCounter, sectionDiv.data('section-id'))}
                        </div>

                        <button type="button" class="addChoice">
                            <img src="<?= base_url('assets/icons/ADD_ICON.webp'); ?>" alt="Add More Choice">
                        </button>
                        <input type="hidden" name="sections[${sectionDiv.data('section-id')}][short_assessment][${multipleChoiceCounter}][sequence_num]" value="${sequenceCounter}">
                        <div class="component-controls">
                            <button type="button" class="swapUp" title="Move Up"><ion-icon name="chevron-up-outline"></ion-icon></button>
                            <button type="button" class="swapDown" title="Move Down"><ion-icon name="chevron-down-outline"></ion-icon></button>
                            <button type="button" class="removeComponent">Remove Component</button>
                        </div>
                    </div>
                </div>
            `;

               addItem(sectionDiv, 'short_assessment', multipleChoiceHtml);
               sequenceCounter++;
               updatePreviewButtonState();

               // Function to generate the initial choices with radio buttons
               function generateInitialChoices(multipleChoiceCounter, sectionId) {
                  let choiceHtml = '';
                  for (let i = 1; i <= 3; i++) {
                     choiceHtml += `
                        <div class="choice-item">
                            <input type="radio" name="sections[${sectionId}][short_assessment][${multipleChoiceCounter}][correct_answer]" value="choice_${i}" required>
                            <input type="text" name="sections[${sectionId}][short_assessment][${multipleChoiceCounter}][choice_${i}]" placeholder="Choice ${i}">
                        </div>
                    `;
                  }
                  return choiceHtml;
               }
            });

            // Event listener to add more choices without radio buttons and with remove functionality
            $('#sections').on('click', '.addChoice', function() {
               let choiceContainer = $(this).siblings('.choices-container');
               let sectionDiv = $(this).closest('.section');
               let sectionId = sectionDiv.data('section-id');
               let multipleChoiceCounter = sectionDiv.find('.multiple_choice.component-item').length; // Current short assessment counter
               let choiceCount = choiceContainer.find('.choice-item').length + 1; // Next choice number

               // Add a new choice input without a radio button and include a "Remove Choice" button
               let newChoiceHtml = `
                <div class="choice-item additional-choice">
                    <input type="text" name="sections[${sectionId}][short_assessment][${multipleChoiceCounter}][choice_${choiceCount}]" placeholder="Choice ${choiceCount}">
                    <button type="button" class="removeChoice">Remove Choice</button>
                </div>
            `;

               choiceContainer.append(newChoiceHtml);
               updateChoiceNumbers(choiceContainer);
            });

            // Event listener to remove additional choices
            $('#sections').on('click', '.removeChoice', function() {
               let choiceContainer = $(this).closest('.choices-container');
               $(this).closest('.choice-item').remove();
               updateChoiceNumbers(choiceContainer);
            });

            // Function to update choice numbers and names sequentially
            function updateChoiceNumbers(choiceContainer) {
               let initialChoiceCount = 3; // The number of initial choices with radio buttons
               choiceContainer.find('.choice-item').each(function(index) {
                  let choiceNumber = index + 1;
                  if (choiceNumber > initialChoiceCount) {
                     // Only update additional choices (beyond the initial 3)
                     $(this).find('input[type="text"]').attr('placeholder', `Choice ${choiceNumber}`);
                     $(this).find('input[type="text"]').attr('name', `sections[${$(this).closest('.section').data('section-id')}][short_assessment][${$(this).closest('.multiple_choice').index() + 1}][choice_${choiceNumber}]`);
                  }
               });
            }




            // Add Multiple Choice Set functionality
            $('#sections').on('click', '.addMultipleChoiceSet', function() {
               let sectionDiv = $(this).closest('.section');
               let sectionId = sectionDiv.data('section-id');
               let setCounter = sectionDiv.find('.multiple_choice_set').length + 1;

               // Generate and append a new multiple-choice set
               let multipleChoiceSetHtml = generateMultipleChoiceSetHtml(sectionId, setCounter);
               sectionDiv.find('.section-components').append(multipleChoiceSetHtml);
               updateSetNumbers(sectionDiv);
               updateSequenceNumbers(sectionDiv);

               // Increment the sequenceCounter for the next set
               sequenceCounter++;
               updatePreviewButtonState();
            });

            // Generate Multiple Choice Set HTML
            function generateMultipleChoiceSetHtml(sectionId, setCounter) {

               let initialQuestionCount = 2;

               return `
                <div class="multiple_choice_set  component-item multiple_choice_set-color" data-set="${setCounter}">

                    <div class="mcs-container-title">
                        <h4>Multiple Choice</h4>
                        <input type="text" name="sections[${sectionId}][multiple_choice_set][${setCounter}][set_title]" placeholder="Multiple Choice Title">
                        <div class="addQuestion-button-container">
                            <div class="component-controls">
                                <button type="button" class="swapUp" title="Move Up"><ion-icon name="chevron-up-outline"></ion-icon></button>
                                <button type="button" class="swapDown" title="Move Down"><ion-icon name="chevron-down-outline"></ion-icon></button>
                                <button type="button" class="removeSet">Remove Component</button>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" name="sections[${sectionId}][multiple_choice_set][${setCounter}][sequence_num]" value="${sequenceCounter}">

                    <div class="questions-container">
                        ${generateMultipleChoiceQuestionHtml(sectionId, setCounter, 1)}
                        ${generateMultipleChoiceQuestionHtml(sectionId, setCounter, 2)}
                    </div>

                    <div class="addQuestion-remove-btn">
                        <button type="button" class="addMoreQuestions">Add More Questions</button>
                        <p class="total-questions">Total Questions: <span>${initialQuestionCount}</span></p> 
                    </div>
                   
                </div>
            `;
            }


            // Generate Multiple Choice Question HTML
            function generateMultipleChoiceQuestionHtml(sectionId, setCounter, questionCounter) {
               return `
                <div class="multiple_choice" data-question="${questionCounter}">
                    <h4>Question ${questionCounter}</h4>
                    <input type="text" name="sections[${sectionId}][multiple_choice_set][${setCounter}][questions][${questionCounter}][question_text]" placeholder="Question">
                    
                    <div class="choices-container">
                        ${generateChoiceOptions(sectionId, setCounter, questionCounter)}
                    </div>
                    
                    <div class="addChoice-button-container">
                        <button type="button" class="addChoice_set">
                            <img src="<?= base_url('assets/icons/ADD_ICON.webp'); ?>" alt="Add More Choice">
                            <span>Add Choice</span>
                        </button>
                        <button type="button" class="removeQuestion">Remove Question</button>
                    </div>
                </div>
            `;
            }

            // Generate Choice Options (3 default choices)
            function generateChoiceOptions(sectionId, setCounter, questionCounter) {
               let choicesHtml = '';
               for (let i = 1; i <= 3; i++) {
                  choicesHtml += `
                    <div class="choice" data-choice="${i}">
                        <input type="radio" name="sections[${sectionId}][multiple_choice_set][${setCounter}][questions][${questionCounter}][correct_ans_num]" value="${i}" required>
                        <input type="text" name="sections[${sectionId}][multiple_choice_set][${setCounter}][questions][${questionCounter}][choices][${i}][choice_text]" placeholder="Choice ${i}">
                        <button type="button" class="removeChoice removeChoice-icon"><ion-icon name="trash-outline"></ion-icon></button>
                    </div>
                `;
               }
               return choicesHtml;
            }

            // Add More Questions
            $('#sections').on('click', '.addMoreQuestions', function() {
               let setDiv = $(this).closest('.multiple_choice_set');
               let sectionId = setDiv.closest('.section').data('section-id');
               let setCounter = setDiv.data('set');

               // Dynamically calculate the questionCounter based on the current number of questions
               let questionCounter = setDiv.find('.multiple_choice').length + 1;

               // Generate the new question HTML
               let newQuestionHtml = generateMultipleChoiceQuestionHtml(sectionId, setCounter, questionCounter);

               // Append the new question to the questions container
               setDiv.find('.questions-container').append(newQuestionHtml);

               // Update the total questions count
               setDiv.find('.total-questions').text(`Total Questions: ${questionCounter}`);
               reindexQuestions(setDiv);
               updatePreviewButtonState();
            });

            // Remove Question and Reindex
            $('#sections').on('click', '.removeQuestion', function() {
               let setDiv = $(this).closest('.multiple_choice_set');
               $(this).closest('.multiple_choice').remove();

               let questionCounter = setDiv.find('.multiple_choice').length;

               setDiv.find('.total-questions').text(`Total Questions: ${questionCounter}`);
               reindexQuestions(setDiv);
               updatePreviewButtonState();
            });

            // Remove Set and Reindex
            $('#sections').on('click', '.removeSet', function() {
               let sectionDiv = $(this).closest('.section');
               $(this).closest('.multiple_choice_set').remove();
               updateSetNumbers(sectionDiv);
               updateSequenceNumbers(sectionDiv);
               updatePreviewButtonState();
            });

            // Add More Choices
            $('#sections').on('click', '.addChoice_set', function() {
               let questionDiv = $(this).closest('.multiple_choice');
               let sectionId = questionDiv.closest('.section').data('section-id');
               let setCounter = questionDiv.closest('.multiple_choice_set').data('set');
               let questionCounter = questionDiv.data('question');
               let choiceCounter = questionDiv.find('.choice').length + 1;

               let newChoiceHtml = `
                <div class="choice" data-choice="${choiceCounter}">
                    <input type="radio" name="sections[${sectionId}][multiple_choice_set][${setCounter}][questions][${questionCounter}][correct_ans_num]" value="${choiceCounter}" required>
                    <input type="text" name="sections[${sectionId}][multiple_choice_set][${setCounter}][questions][${questionCounter}][choices][${choiceCounter}][choice_text]" placeholder="Choice ${choiceCounter}">
                    <button type="button" class="removeChoice"><ion-icon name="trash-outline"></ion-icon></button>
                </div>
            `;

               questionDiv.find('.choices-container').append(newChoiceHtml);
            });



            // Remove Choice
            $('#sections').on('click', '.removeChoice', function() {
               let questionDiv = $(this).closest('.multiple_choice');
               let choiceContainer = questionDiv.find('.choices-container');

               // Remove the selected choice
               $(this).closest('.choice').remove();

               // Reindex the remaining choices
               reindexChoices(choiceContainer);
            });

            // Function to reindex choices
            function reindexChoices(choiceContainer) {
               choiceContainer.find('.choice').each(function(index) {
                  let choiceNumber = index + 1;

                  // Update the placeholder for the choice text
                  $(this).find('input[type="text"]').attr('placeholder', `Choice ${choiceNumber}`);

                  // Update the name attributes for the choice text and radio button
                  let questionDiv = $(this).closest('.multiple_choice');
                  let sectionId = questionDiv.closest('.section').data('section-id');
                  let setCounter = questionDiv.closest('.multiple_choice_set').data('set');
                  let questionCounter = questionDiv.data('question');

                  $(this).find('input[type="text"]').attr(
                     'name',
                     `sections[${sectionId}][multiple_choice_set][${setCounter}][questions][${questionCounter}][choices][${choiceNumber}][choice_text]`
                  );
                  $(this).find('input[type="radio"]').attr(
                     'name',
                     `sections[${sectionId}][multiple_choice_set][${setCounter}][questions][${questionCounter}][correct_ans_num]`
                  ).val(choiceNumber);
               });
            }

            // ...existing code...

            // Update Set Numbers (Ensures correct numbering when removing sets)
            function updateSetNumbers(sectionDiv) {
               sectionDiv.find('.multiple_choice_set').each(function(index) {
                  $(this).attr('data-set', index + 1);
                  $(this).find('h4').text(`Multiple Choice`);
                  reindexQuestions($(this));
               });
            }

            // Reindex Questions (Ensures correct numbering when removing questions)
            function reindexQuestions(setDiv) {
               setDiv.find('.multiple_choice').each(function(index) {
                  $(this).attr('data-question', index + 1);
                  $(this).find('h4').text(`Question ${index + 1}`);
               });
            }


            // Remove component functionality
            $('#sections').on('click', '.removeComponent', function() {
               let sectionDiv = $(this).closest('.section');
               $(this).closest('.component-item').remove();
               reindexComponents(sectionDiv);
               updateSequenceNumbers(sectionDiv);
               updatePreviewButtonState();
            });

            // Swap Up functionality
            $('#sections').on('click', '.swapUp', function() {
               let currentComponent = $(this).closest('.component-item');
               let prevComponent = currentComponent.prev('.component-item');

               if (prevComponent.length > 0) {
                  currentComponent.insertBefore(prevComponent);
                  let sectionDiv = $(this).closest('.section');
                  reindexComponents(sectionDiv);
                  updateSequenceNumbers(sectionDiv);
               }
            });

            // Swap Down functionality
            $('#sections').on('click', '.swapDown', function() {
               let currentComponent = $(this).closest('.component-item');
               let nextComponent = currentComponent.next('.component-item');

               if (nextComponent.length > 0) {
                  currentComponent.insertAfter(nextComponent);
                  let sectionDiv = $(this).closest('.section');
                  reindexComponents(sectionDiv);
                  updateSequenceNumbers(sectionDiv);
               }
            });

            // Function to update sequence numbers based on actual order
            function updateSequenceNumbers(sectionDiv) {
               sectionDiv.find('.component-item').each(function(index) {
                  let sequenceInput = $(this).find('input[name*="[sequence_num]"]');
                  if (sequenceInput.length > 0) {
                     sequenceInput.val(index + 1);
                  }
               });
            }
         });
      </script>


      <script>
         var formChanged = false; // Initially, the form is not changed
         var formSubmitted = false; // Flag to detect if the form is being submitted

         // Function to detect changes in form elements
         function detectChanges() {
            formChanged = true; // Set the flag to true when a change is detected
         }

         // Add event listeners to input and textarea elements to detect changes
         document.addEventListener('DOMContentLoaded', function() {
            // Select all input, textarea elements that are not of type button, submit, or reset
            var inputs = document.querySelectorAll('input:not([type="button"]):not([type="submit"]):not([type="reset"]), textarea');

            // Add event listeners for detecting changes
            inputs.forEach(function(input) {
               input.addEventListener('input', detectChanges);
               input.addEventListener('change', detectChanges); // Handle change events for non-text inputs
               input.addEventListener('keyup', detectChanges); // Handle keyup events for better real-time detection
            });

            // Add a click listener to the submit button to show confirmation prompt
            var submitButton = document.getElementById('submitButton');

            if (submitButton) {
               submitButton.addEventListener('click', function(e) {
                  e.preventDefault(); // Always prevent default submission initially
                  // Show custom confirmation dialog for submission
                  showSubmissionConfirmationPanel();
               });
            }

         });

         // Custom leave confirmation panel
         let pendingNavigation = null;
         let originalClickHandler = null;

         // Create custom confirmation panel
         function createLeaveConfirmationPanel() {
            const panel = document.createElement('div');
            panel.id = 'leave-confirmation-panel';
            panel.className = 'leave-confirmation-overlay';
            panel.innerHTML = `
               <div class="leave-confirmation-content">
                  <div class="leave-confirmation-header">
                     <h3>Leave Site?</h3>
                  </div>
                  <div class="leave-confirmation-body">
                     <p>Changes you made may not be saved.</p>
                  </div>
                  <div class="leave-confirmation-buttons">
                     <button id="leave-confirm-btn" class="leave-btn leave-btn-primary">Leave</button>
                     <button id="leave-cancel-btn" class="leave-btn leave-btn-secondary">Cancel</button>
                  </div>
               </div>
            `;
            document.body.appendChild(panel);

            // Add event listeners
            document.getElementById('leave-confirm-btn').addEventListener('click', function() {
               hideLeaveConfirmationPanel();
               if (pendingNavigation) {
                  formSubmitted = true; // Prevent further prompts
                  if (pendingNavigation.type === 'link') {
                     window.location.href = pendingNavigation.url;
                  } else if (pendingNavigation.type === 'beforeunload') {
                     // For beforeunload, we can't actually proceed, but we mark as submitted
                     pendingNavigation.callback();
                  }
               }
            });

            document.getElementById('leave-cancel-btn').addEventListener('click', function() {
               hideLeaveConfirmationPanel();
               // Hide global loading panel if it was shown
               hideGlobalLoadingPanel();
               pendingNavigation = null;
            });

            // Allow clicking outside the panel to cancel
            panel.addEventListener('click', function(e) {
               if (e.target === panel) {
                  hideLeaveConfirmationPanel();
                  hideGlobalLoadingPanel();
                  pendingNavigation = null;
               }
            });

            return panel;
         }

         function showLeaveConfirmationPanel() {
            let panel = document.getElementById('leave-confirmation-panel');
            if (!panel) {
               panel = createLeaveConfirmationPanel();
            }
            panel.style.display = 'flex';
         }

         function hideLeaveConfirmationPanel() {
            const panel = document.getElementById('leave-confirmation-panel');
            if (panel) {
               panel.style.display = 'none';
            }
         }

         function hideGlobalLoadingPanel() {
            // Multiple ways to hide the global loading panel for maximum compatibility
            const globalPanel = document.getElementById('global-loading-panel');
            if (globalPanel) {
               globalPanel.style.display = 'none';
            }
            // Also try jQuery method if available
            if (typeof $ !== 'undefined') {
               $('#global-loading-panel').hide();
            }
         }

         // Custom submission confirmation panel
         function createSubmissionConfirmationPanel() {
            const panel = document.createElement('div');
            panel.id = 'submission-confirmation-panel';
            panel.className = 'leave-confirmation-overlay';
            panel.innerHTML = `
               <div class="leave-confirmation-content">
                  <div class="leave-confirmation-header">
                     <h3>Confirm Submission?</h3>
                  </div>
                  <div class="leave-confirmation-body">
                     <p>Are you sure you want to publish this module? This action will make the module available to users.</p>
                  </div>
                  <div class="leave-confirmation-buttons">
                     <button id="submission-confirm-btn" class="leave-btn leave-btn-primary">Publish Module</button>
                     <button id="submission-cancel-btn" class="leave-btn leave-btn-secondary">Cancel</button>
                  </div>
               </div>
            `;
            document.body.appendChild(panel);

            // Add event listeners
            document.getElementById('submission-confirm-btn').addEventListener('click', function() {
               hideSubmissionConfirmationPanel();
               formSubmitted = true; // Set the formSubmitted flag to true if confirmed
               // Call the proper submitForm function to handle file uploads and progress
               const event = new Event('submit', { bubbles: true, cancelable: true });
               submitForm(event);
            });

            document.getElementById('submission-cancel-btn').addEventListener('click', function() {
               hideSubmissionConfirmationPanel();
            });

            // Allow clicking outside the panel to cancel
            panel.addEventListener('click', function(e) {
               if (e.target === panel) {
                  hideSubmissionConfirmationPanel();
               }
            });

            return panel;
         }

         function showSubmissionConfirmationPanel() {
            let panel = document.getElementById('submission-confirmation-panel');
            if (!panel) {
               panel = createSubmissionConfirmationPanel();
            }
            panel.style.display = 'flex';
         }

         function hideSubmissionConfirmationPanel() {
            const panel = document.getElementById('submission-confirmation-panel');
            if (panel) {
               panel.style.display = 'none';
            }
         }

         // Override link clicks to show custom confirmation
         // We need to bind this with higher priority than the global loading panel handler
         $(document).on('click', 'a', function(e) {
            if (formChanged && !formSubmitted) {
               const href = $(this).attr('href');
               if (href && href !== '#' && href.toLowerCase() !== 'javascript:void(0)') {
                  e.preventDefault();
                  e.stopImmediatePropagation(); // Prevent other handlers including global loading panel

                  // Store the navigation details
                  pendingNavigation = {
                     type: 'link',
                     url: href
                  };

                  // Show custom confirmation panel
                  showLeaveConfirmationPanel();
                  return false;
               }
            }
         });

         // Handle beforeunload with custom panel (fallback for direct navigation)
         window.addEventListener('beforeunload', function(e) {
            if (formChanged && !formSubmitted) {
               // For beforeunload, we still need to use the browser's native dialog
               // as we cannot show custom panels during beforeunload
               var confirmationMessage = "Are you sure you want to leave? Your changes may not be saved.";
               (e || window.event).returnValue = confirmationMessage;
               return confirmationMessage;
            }
         });
      </script>




      <script>
         function previewFile(inputElementId, previewElementId) {
            var fileInput = document.getElementById(inputElementId); // Get the file input element
            var previewElement = document.getElementById(previewElementId); // Get the preview element
            var loadingPanel = document.getElementById(`loading-panel-${previewElementId}`);

            // Clear any previous content
            previewElement.innerHTML = '';
            loadingPanel.style.display = 'flex'; // Show loading panel

            // Check if a file is selected
            if (fileInput.files && fileInput.files[0]) {
               var file = fileInput.files[0]; // Get the selected file
               var reader = new FileReader(); // Create a FileReader object
               var fileType = file.type; // Get the file's MIME type

               // Generate unique file ID for progress tracking
               const fileId = `${inputElementId}_${Date.now()}`;

               // Add to upload progress manager
               if (window.UploadProgressManager) {
                  UploadProgressManager.addUpload(fileId, file.name, file.size, file.type);

                  // Simulate upload progress for preview loading
                  let progress = 0;
                  const progressInterval = setInterval(() => {
                     progress += Math.random() * 15;
                     if (progress >= 100) {
                        progress = 100;
                        clearInterval(progressInterval);
                        UploadProgressManager.completeUpload(fileId);
                     }
                     UploadProgressManager.updateProgress(fileId, progress);
                  }, 150);
               }



               reader.onload = function(e) {
                  // Hide loading panel
                  loadingPanel.style.display = 'none';

                  // Handle image files (jpg, png, gif, etc.)
                  if (fileType.startsWith('image/')) {
                     var img = document.createElement('img');
                     img.src = e.target.result;
                     previewElement.appendChild(img);

                     // Add remove button directly to the previewElement
                     var removeButton = document.createElement('button');
                     removeButton.innerHTML = '<ion-icon name="trash-outline"></ion-icon>';
                     removeButton.classList.add('remove-btn-main');
                     removeButton.onclick = function() {
                        previewElement.innerHTML = ''; // Clear preview
                        fileInput.value = ''; // Reset the file input
                     };
                     previewElement.appendChild(removeButton); // Append the button to the previewElement
                  }

                  // Handle PDF files
                  else if (fileType === 'application/pdf') {
                     var iframe = document.createElement('iframe');
                     iframe.src = e.target.result;
                     previewElement.appendChild(iframe);

                     // Create a remove button for the PDF file preview
                     var removeButton = document.createElement('button');
                     removeButton.innerHTML = '<ion-icon name="trash-outline"></ion-icon>';
                     removeButton.classList.add('remove-btn-main');
                     removeButton.onclick = function() {
                        previewElement.innerHTML = ''; // Clear the preview
                        fileInput.value = ''; // Reset the file input
                     };

                     // Append the remove button to the previewElement
                     previewElement.appendChild(removeButton);
                  }


                  // Handle video files
                  else if (fileType.startsWith('video/')) {
                     var video = document.createElement('video');
                     video.src = e.target.result;
                     video.controls = true;
                     previewElement.appendChild(video);

                     // Create a parent div for file size, duration, and remove button
                     var infoDiv = document.createElement('div');
                     infoDiv.className = 'file-information-container';
                     previewElement.appendChild(infoDiv);

                     // var infoTitle = document.createElement('h3');
                     // infoTitle.textContent = 'Video File Information';
                     // infoDiv.appendChild(infoTitle);

                     // // Add file name
                     // var fileNameElement = document.createElement('p');
                     // fileNameElement.innerHTML = `<span class="info-title">File name</span> ${file.name}`;
                     // infoDiv.appendChild(fileNameElement)

                     // // Add file size information
                     // var fileSizeElement = document.createElement('p');
                     // var fileSizeMB = (file.size / 1024 / 1024).toFixed(2); // Convert bytes to MB
                     // fileSizeElement.innerHTML = `<span class="info-title">File size</span> ${fileSizeMB} MB`;
                     // infoDiv.appendChild(fileSizeElement);

                     // Add event listener to get video duration
                     video.addEventListener('loadedmetadata', function() {
                        // var duration = video.duration;
                        // var minutes = Math.floor(duration / 60);
                        // var seconds = Math.floor(duration % 60);
                        // var durationText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                        // var durationElement = document.createElement('p');
                        // durationElement.innerHTML = `<span class="info-title">Duration</span> ${durationText}`;
                        // infoDiv.appendChild(durationElement);

                        // Add remove button after duration element
                        var removeButton = document.createElement('button');
                        removeButton.innerHTML = '<ion-icon name="trash-outline">';
                        removeButton.onclick = function() {
                           previewElement.innerHTML = '';
                           fileInput.value = '';
                        };
                        infoDiv.appendChild(removeButton);
                     });
                  }


                  // Handle audio files
                  else if (fileType.startsWith('audio/')) {
                     var audio = document.createElement('audio');
                     audio.src = e.target.result;
                     audio.controls = true;
                     previewElement.appendChild(audio);

                     // Create a remove button for the audio file preview
                     var removeButton = document.createElement('button');
                     removeButton.innerHTML = '<ion-icon name="trash-outline">';
                     removeButton.classList.add('remove-btn-main');
                     removeButton.onclick = function() {
                        previewElement.innerHTML = ''; // Clear the preview
                        fileInput.value = ''; // Reset the file input
                     };

                     // Append the remove button to the previewElement
                     previewElement.appendChild(removeButton);
                  }


                  // Handle unsupported file types (e.g., .docx, .zip, etc.)
                  else {
                     var message = document.createElement('p');
                     message.textContent = "Cannot preview this file. File type: " + fileType;
                     previewElement.appendChild(message);

                     // Optionally, provide a download link
                     var link = document.createElement('a');
                     link.href = e.target.result;
                     link.download = file.name; // Set file name for download
                     link.textContent = "Download " + file.name;
                     previewElement.appendChild(link);
                  }


               };

               // Add error handling for FileReader
               reader.onerror = function() {
                  if (window.UploadProgressManager) {
                     UploadProgressManager.errorUpload(fileId, 'Failed to read file');
                  }
                  loadingPanel.style.display = 'none';
                  previewElement.innerHTML = '<p>Error loading file preview</p>';
               };

               // Read the file as a Data URL for preview
               reader.readAsDataURL(file);
            }

         }
      </script>



      <script>
         // Select all the buttons with the class 'removeSection'
         document.querySelectorAll('.removeSection').forEach(button => {
            button.addEventListener('click', function() {
               // Display a confirmation dialog to the user
               const confirmation = confirm("Are you sure you want to remove this section?");
               if (confirmation) {
                  // If the user confirms, remove the section
                  this.closest('.section-content').remove();
               }
               // If the user cancels, do nothing
            });
         });
      </script>


   </div>
</div>


<script>
   AOS.init();
</script>